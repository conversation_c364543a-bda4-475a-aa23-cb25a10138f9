// ==UserScript==
// @name         斗鱼自动全屏解决方案
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  单击播放器自动触发全屏
// <AUTHOR>
// @match        *://*.douyu.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 单击播放器触发全屏
    function addPlayerClickFullscreen() {
        // 播放器可能的选择器
        const playerSelectors = [
            '.layout-Player-video',     // 播放器容器
            '#room-html5-player',       // HTML5播放器
            '.room-Player-Box',         // 播放器盒子
            '.PlayerContainer',         // 播放器容器
            'video'                     // 视频元素
        ];

        function attachClickListener() {
            let playerElement = null;

            // 查找播放器元素
            for (const selector of playerSelectors)
            {
                playerElement = document.querySelector(selector);
                if (playerElement)
                {
                    console.log(`找到播放器元素: ${selector}`);
                    break;
                }
            }

            if (!playerElement)
            {
                console.log('未找到播放器元素，1秒后重试');
                setTimeout(attachClickListener, 1000);
                return;
            }

            // 添加点击事件监听器
            playerElement.addEventListener('click', function(event) {
                // 防止事件冒泡
                event.stopPropagation();

                // 等待全屏按钮加载并点击
                waitForElement("div.fs-781153", (element) => {
                    element.click();
                    console.log('点击播放器已触发全屏');
                });
            });

            console.log('播放器点击全屏监听器已添加');
        }

        // 开始尝试添加监听器
        attachClickListener();
    }

    // 通用的等待元素加载函数
    function waitForElement(selector, callback, maxAttempts = 50) {
        let attempts = 0;
        const interval = setInterval(() => {
            const element = document.querySelector(selector);
            if (element)
            {
                clearInterval(interval);
                callback(element);
            } else if (++attempts >= maxAttempts)
            {
                clearInterval(interval);
                console.log(`元素 ${selector} 未找到，已达到最大尝试次数`);
            }
        }, 200);
    }

    // 检测当前页面是否为直播间页面
    function isLiveRoomPage() {
        return window.location.pathname.includes('/') &&
            (document.querySelector('.layout-Player-video') ||
                document.querySelector('#room-html5-player'));
    }

    // 主函数
    function init() {
        if (!isLiveRoomPage())
        {
            console.log('非直播间页面，跳过全屏功能');
            return;
        }

        console.log('斗鱼自动全屏脚本已启动');

        // 添加播放器点击全屏功能
        addPlayerClickFullscreen();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading')
    {
        document.addEventListener('DOMContentLoaded', init);
    } else
    {
        init();
    }

})();

// ==UserScript==
// @name         斗鱼自动全屏解决方案
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  等待用户交互后自动触发全屏
// <AUTHOR>
// @match        *://*.douyu.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 监听用户的第一次交互，然后触发全屏
    function waitForUserInteractionThenFullscreen() {
        let hasInteracted = false;

        function triggerFullscreen() {
            if (hasInteracted) return;
            hasInteracted = true;

            // 等待全屏按钮加载
            waitForElement("div.fs-781153", (element) => {
                element.click();
                console.log('已自动触发全屏');
            });
        }

        // 监听各种用户交互事件
        const events = ['click', 'keydown', 'mousedown', 'touchstart'];
        events.forEach(eventType => {
            document.addEventListener(eventType, triggerFullscreen, { once: true });
        });
    }

    // 通用的等待元素加载函数
    function waitForElement(selector, callback, maxAttempts = 50) {
        let attempts = 0;
        const interval = setInterval(() => {
            const element = document.querySelector(selector);
            if (element)
            {
                clearInterval(interval);
                callback(element);
            } else if (++attempts >= maxAttempts)
            {
                clearInterval(interval);
                console.log(`元素 ${selector} 未找到，已达到最大尝试次数`);
            }
        }, 200);
    }

    // 检测当前页面是否为直播间页面
    function isLiveRoomPage() {
        return window.location.pathname.includes('/') &&
            (document.querySelector('.layout-Player-video') ||
                document.querySelector('#room-html5-player'));
    }

    // 主函数
    function init() {
        if (!isLiveRoomPage())
        {
            console.log('非直播间页面，跳过全屏功能');
            return;
        }

        console.log('斗鱼自动全屏脚本已启动');

        // 使用等待用户交互方案
        waitForUserInteractionThenFullscreen();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading')
    {
        document.addEventListener('DOMContentLoaded', init);
    } else
    {
        init();
    }

})();

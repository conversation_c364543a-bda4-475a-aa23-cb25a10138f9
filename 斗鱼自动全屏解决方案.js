// ==UserScript==
// @name         斗鱼自动全屏解决方案
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  解决浏览器全屏API用户交互限制的多种方案
// <AUTHOR>
// @match        *://*.douyu.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 方案1：监听用户的第一次交互，然后触发全屏
    function waitForUserInteractionThenFullscreen() {
        let hasInteracted = false;
        
        function triggerFullscreen() {
            if (hasInteracted) return;
            hasInteracted = true;
            
            // 等待全屏按钮加载
            waitForElement("div.fs-781153", (element) => {
                element.click();
                console.log('已自动触发全屏');
            });
        }
        
        // 监听各种用户交互事件
        const events = ['click', 'keydown', 'mousedown', 'touchstart'];
        events.forEach(eventType => {
            document.addEventListener(eventType, triggerFullscreen, { once: true });
        });
    }

    // 方案2：模拟真实的鼠标点击事件
    function simulateRealClick(element) {
        // 创建更真实的鼠标事件序列
        const events = [
            new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                buttons: 1,
                clientX: element.getBoundingClientRect().left + 10,
                clientY: element.getBoundingClientRect().top + 10
            }),
            new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                buttons: 0,
                clientX: element.getBoundingClientRect().left + 10,
                clientY: element.getBoundingClientRect().top + 10
            }),
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window,
                button: 0,
                buttons: 0,
                clientX: element.getBoundingClientRect().left + 10,
                clientY: element.getBoundingClientRect().top + 10
            })
        ];
        
        events.forEach(event => {
            element.dispatchEvent(event);
        });
    }

    // 方案3：使用页面加载完成后的短暂延迟（利用用户可能的交互窗口）
    function delayedFullscreen() {
        // 页面加载完成后等待用户可能的交互
        window.addEventListener('load', () => {
            setTimeout(() => {
                waitForElement("div.fs-781153", (element) => {
                    // 尝试直接点击
                    element.click();
                    
                    // 如果直接点击失败，尝试模拟真实点击
                    setTimeout(() => {
                        if (!document.fullscreenElement) {
                            simulateRealClick(element);
                        }
                    }, 100);
                });
            }, 1000); // 1秒延迟，给用户交互留出时间
        });
    }

    // 方案4：使用双击事件（参考简单斗鱼脚本）
    function doubleClickFullscreen() {
        waitForElement("div.fs-781153", (element) => {
            // 模拟双击事件
            const dblClickEvent = new MouseEvent('dblclick', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            element.dispatchEvent(dblClickEvent);
        });
    }

    // 方案5：监听页面可见性变化（用户切换标签页时触发）
    function visibilityChangeFullscreen() {
        let hasTriggered = false;
        
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !hasTriggered) {
                hasTriggered = true;
                setTimeout(() => {
                    waitForElement("div.fs-781153", (element) => {
                        element.click();
                    });
                }, 500);
            }
        });
    }

    // 通用的等待元素加载函数（参考白piao斗鱼脚本）
    function waitForElement(selector, callback, maxAttempts = 50) {
        let attempts = 0;
        const interval = setInterval(() => {
            const element = document.querySelector(selector);
            if (element) {
                clearInterval(interval);
                callback(element);
            } else if (++attempts >= maxAttempts) {
                clearInterval(interval);
                console.log(`元素 ${selector} 未找到，已达到最大尝试次数`);
            }
        }, 200);
    }

    // 方案6：创建用户提示按钮（最可靠的方案）
    function createFullscreenButton() {
        // 创建一个浮动按钮，用户点击后触发全屏
        const button = document.createElement('div');
        button.innerHTML = '🔲 点击全屏';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: #ff6600;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;
        
        button.addEventListener('mouseenter', () => {
            button.style.background = '#ff8533';
            button.style.transform = 'scale(1.05)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.background = '#ff6600';
            button.style.transform = 'scale(1)';
        });
        
        button.addEventListener('click', () => {
            waitForElement("div.fs-781153", (element) => {
                element.click();
                button.style.display = 'none'; // 隐藏按钮
            });
        });
        
        document.body.appendChild(button);
        
        // 5秒后自动隐藏按钮
        setTimeout(() => {
            if (button.parentNode) {
                button.style.opacity = '0.5';
                setTimeout(() => {
                    button.remove();
                }, 3000);
            }
        }, 5000);
    }

    // 方案7：组合方案（推荐使用）
    function combinedApproach() {
        // 首先尝试等待用户交互
        waitForUserInteractionThenFullscreen();
        
        // 同时创建提示按钮作为备选
        setTimeout(createFullscreenButton, 2000);
        
        // 监听页面可见性变化
        visibilityChangeFullscreen();
    }

    // 检测当前页面是否为直播间页面
    function isLiveRoomPage() {
        return window.location.pathname.includes('/') && 
               (document.querySelector('.layout-Player-video') || 
                document.querySelector('#room-html5-player'));
    }

    // 主函数
    function init() {
        if (!isLiveRoomPage()) {
            console.log('非直播间页面，跳过全屏功能');
            return;
        }

        console.log('斗鱼自动全屏脚本已启动');
        
        // 使用组合方案（推荐）
        combinedApproach();
        
        // 或者选择单一方案：
        // waitForUserInteractionThenFullscreen(); // 方案1
        // delayedFullscreen(); // 方案3
        // createFullscreenButton(); // 方案6
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();

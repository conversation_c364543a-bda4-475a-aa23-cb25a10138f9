// ==UserScript==
// @name         斗鱼显示控制面板
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动显示播放器控制面板
// <AUTHOR>
// @match        *://*.douyu.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 显示控制面板
    function showControlBar() {
        // 查找控制面板元素
        const controlBar = document.querySelector('.controlbar-f41e38');
        
        if (controlBar) {
            // 移除隐藏类
            controlBar.classList.remove('hide-6cf943');
            console.log('控制面板已显示');
            return true;
        }
        
        return false;
    }

    // 等待元素加载并显示控制面板
    function waitAndShowControlBar() {
        let attempts = 0;
        const maxAttempts = 50;
        
        const interval = setInterval(() => {
            if (showControlBar()) {
                clearInterval(interval);
            } else if (++attempts >= maxAttempts) {
                clearInterval(interval);
                console.log('未找到控制面板元素');
            }
        }, 200);
    }

    // 使用MutationObserver监听DOM变化，防止控制面板被重新隐藏
    function observeControlBar() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('controlbar-f41e38') && 
                        target.classList.contains('hide-6cf943')) {
                        // 如果控制面板被重新隐藏，立即显示
                        target.classList.remove('hide-6cf943');
                        console.log('控制面板被重新显示');
                    }
                }
            });
        });

        // 开始观察
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['class']
        });

        console.log('控制面板监听器已启动');
    }

    // 检测当前页面是否为直播间页面
    function isLiveRoomPage() {
        return window.location.pathname.includes('/') && 
               (document.querySelector('.layout-Player-video') || 
                document.querySelector('#room-html5-player') ||
                document.querySelector('.video-container-dbc7dc'));
    }

    // 主函数
    function init() {
        if (!isLiveRoomPage()) {
            console.log('非直播间页面，跳过控制面板显示功能');
            return;
        }

        console.log('斗鱼控制面板显示脚本已启动');
        
        // 等待并显示控制面板
        waitAndShowControlBar();
        
        // 启动监听器防止被重新隐藏
        observeControlBar();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
